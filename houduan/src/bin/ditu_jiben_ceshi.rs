use houduan::chushihua::shujukuxitong::youxishujuchuli::ditushuju::{
    ditu_zifuchuan_changliangguanli, ditu_jiben_xinxi, ditu_huizong_xinxi, 
    ditu_wanzheng_xinxi, ditu_liebiao_xiang, ditu_liebiao_fenye_canshu
};

#[tokio::main]
async fn main() {
    println!("开始运行地图数据基本功能测试...\n");

    // 测试常量管理
    ceshi_changliangguanli();

    // 测试数据结构
    ceshi_shujujiegou();

    println!("\n地图数据基本功能测试完成！");
}

/// 测试常量管理功能
fn ceshi_changliangguanli() {
    println!("=== 测试常量管理功能 ===");

    // 测试查询模式常量
    println!("查询模式常量:");
    println!("  - 全部信息查询: {}", ditu_zifuchuan_changliangguanli::chaxun_moshi_quanbu_xinxi);

    // 测试表名常量
    println!("数据库表名常量:");
    println!("  - ditu_name表: {}", ditu_zifuchuan_changliangguanli::biao_ming_ditu_name);
    println!("  - ditu_huizong表: {}", ditu_zifuchuan_changliangguanli::biao_ming_ditu_huizong);

    // 测试Redis键名生成
    let ditu_id = "test_map_001";
    let redis_jian = ditu_zifuchuan_changliangguanli::shengcheng_redis_jian_ditu_quanbu(ditu_id);
    println!("Redis键名生成:");
    println!("  - 地图{}的Redis键: {}", ditu_id, redis_jian);

    let liebiao_jian = ditu_zifuchuan_changliangguanli::shengcheng_redis_jian_ditu_liebiao(10, 1);
    println!("  - 地图列表Redis键: {}", liebiao_jian);

    // 测试错误信息生成
    println!("错误信息生成:");
    let cuowu_xinxi = ditu_zifuchuan_changliangguanli::shengcheng_cuowu_ditu_bucunzai(ditu_id);
    println!("  - 地图不存在错误: {}", cuowu_xinxi);

    let yeshu_cuowu = ditu_zifuchuan_changliangguanli::shengcheng_cuowu_yeshu_chaochufanwei(5, 3);
    println!("  - 页数超出范围错误: {}", yeshu_cuowu);

    // 测试统计信息生成
    println!("统计信息生成:");
    let tongji_xinxi = ditu_zifuchuan_changliangguanli::shengcheng_tongji_ditu_quanbu_huancun(25);
    println!("  - 地图缓存统计: {}", tongji_xinxi);

    println!("✓ 常量管理功能测试完成\n");
}

/// 测试数据结构功能
fn ceshi_shujujiegou() {
    println!("=== 测试数据结构功能 ===");

    // 测试地图基础信息结构体
    let jiben_xinxi = ditu_jiben_xinxi {
        id: Some("map_001".to_string()),
        name: Some("测试地图".to_string()),
    };
    println!("地图基础信息:");
    println!("  - ID: {:?}", jiben_xinxi.id);
    println!("  - 名称: {:?}", jiben_xinxi.name);

    // 测试地图汇总信息结构体
    let huizong_xinxi = ditu_huizong_xinxi {
        ditu_id: Some("map_001".to_string()),
        ditu_mingcheng: Some("测试地图".to_string()),
        xianshi_mingcheng: Some("测试地图显示名".to_string()),
        leixing_town: Some("1".to_string()),
        zhu_fenlei: Some("城镇".to_string()),
        guaiwu_zongshu: Some(10),
        npc_zongshu: Some(5),
        leixing_field: None,
        leixing_dungeon: None,
        leixing_quest: None,
        leixing_instance: None,
        leixing_siege: None,
        leixing_pvp: None,
        leixing_other: None,
        leixing_weizhi: None,
        fenlei_town: None,
        fenlei_field: None,
        fenlei_dungeon: None,
        fenlei_quest: None,
        fenlei_instance: None,
        fenlei_siege: None,
        fenlei_pvp: None,
        fenlei_other: None,
        fenlei_weizhi: None,
        you_guaiwu: None,
        you_npc: None,
        you_yinyue: None,
        you_tupian: None,
        tongzhi_jinru: None,
        tupian_shuliang: None,
        xinxi_biaoti: None,
        fu_biaoti: None,
        zhu_biaoti: None,
        beijing_tupian: None,
        yinyue_wenjian: None,
        riqi: None,
        suoyou_fenlei: None,
        liebiao_fenlei: None,
        jichuxinxi_yaml: None,
        guaiwu_liebiao_yaml: None,
        npc_liebiao_yaml: None,
        ditu_tupian_yaml: None,
        benditupian_lujing_yaml: None,
        you_jichuxinxi: None,
        you_guaiwuxinxi: None,
        you_npcxinxi: None,
        huizong_riqi: None,
    };
    println!("地图汇总信息:");
    println!("  - 地图ID: {:?}", huizong_xinxi.ditu_id);
    println!("  - 显示名称: {:?}", huizong_xinxi.xianshi_mingcheng);
    println!("  - 主分类: {:?}", huizong_xinxi.zhu_fenlei);
    println!("  - 怪物总数: {:?}", huizong_xinxi.guaiwu_zongshu);

    // 测试完整地图信息结构体
    let wanzheng_xinxi = ditu_wanzheng_xinxi {
        id: "map_001".to_string(),
        jiben_xinxi: Some(jiben_xinxi),
        huizong_xinxi: Some(huizong_xinxi),
    };
    println!("完整地图信息:");
    println!("  - 地图ID: {}", wanzheng_xinxi.id);
    println!("  - 包含基础信息: {}", wanzheng_xinxi.jiben_xinxi.is_some());
    println!("  - 包含汇总信息: {}", wanzheng_xinxi.huizong_xinxi.is_some());

    // 测试地图列表项结构体
    let liebiao_xiang = ditu_liebiao_xiang {
        ditu_id: "map_001".to_string(),
        ditu_mingcheng: "测试地图".to_string(),
        xianshi_mingcheng: Some("测试地图显示名".to_string()),
        zhu_fenlei: Some("城镇".to_string()),
    };
    println!("地图列表项:");
    println!("  - 地图ID: {}", liebiao_xiang.ditu_id);
    println!("  - 地图名称: {}", liebiao_xiang.ditu_mingcheng);
    println!("  - 主分类: {:?}", liebiao_xiang.zhu_fenlei);

    // 测试分页参数结构体
    let fenye_canshu = ditu_liebiao_fenye_canshu {
        meiye_shuliang: 20,
        dangqian_ye: 1,
    };
    println!("分页参数:");
    println!("  - 每页数量: {}", fenye_canshu.meiye_shuliang);
    println!("  - 当前页数: {}", fenye_canshu.dangqian_ye);

    // 测试JSON序列化
    match serde_json::to_string(&wanzheng_xinxi) {
        Ok(json_str) => {
            println!("JSON序列化测试:");
            println!("  - JSON长度: {} 字符", json_str.len());
            println!("  - JSON前100字符: {}", &json_str[..json_str.len().min(100)]);
        }
        Err(e) => {
            println!("✗ JSON序列化失败: {}", e);
        }
    }

    println!("✓ 数据结构功能测试完成\n");
}
