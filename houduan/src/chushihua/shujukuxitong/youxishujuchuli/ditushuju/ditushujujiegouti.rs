#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// ditu_name表基础信息结构体
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ditu_jiben_xinxi {
    /// 地图ID
    pub id: Option<String>,
    /// 地图名称
    pub name: Option<String>,
}

/// ditu_huizong表汇总信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_huizong_xinxi {
    /// 地图ID
    pub ditu_id: Option<String>,
    /// 地图名称
    pub ditu_mingcheng: Option<String>,
    /// 显示名称
    pub xianshi_mingcheng: Option<String>,
    /// 类型-城镇
    pub leixing_town: Option<String>,
    /// 类型-野外
    pub leixing_field: Option<String>,
    /// 类型-地下城
    pub leixing_dungeon: Option<String>,
    /// 类型-任务
    pub leixing_quest: Option<String>,
    /// 类型-副本
    pub leixing_instance: Option<String>,
    /// 类型-攻城
    pub leixing_siege: Option<String>,
    /// 类型-PVP
    pub leixing_pvp: Option<String>,
    /// 类型-其他
    pub leixing_other: Option<String>,
    /// 类型-位置
    pub leixing_weizhi: Option<String>,
    /// 分类-城镇
    pub fenlei_town: Option<String>,
    /// 分类-野外
    pub fenlei_field: Option<String>,
    /// 分类-地下城
    pub fenlei_dungeon: Option<String>,
    /// 分类-任务
    pub fenlei_quest: Option<String>,
    /// 分类-副本
    pub fenlei_instance: Option<String>,
    /// 分类-攻城
    pub fenlei_siege: Option<String>,
    /// 分类-PVP
    pub fenlei_pvp: Option<String>,
    /// 分类-其他
    pub fenlei_other: Option<String>,
    /// 分类-位置
    pub fenlei_weizhi: Option<String>,
    /// 有怪物
    pub you_guaiwu: Option<String>,
    /// 有NPC
    pub you_npc: Option<String>,
    /// 有音乐
    pub you_yinyue: Option<String>,
    /// 有图片
    pub you_tupian: Option<String>,
    /// 通知进入
    pub tongzhi_jinru: Option<String>,
    /// 怪物总数
    pub guaiwu_zongshu: Option<i32>,
    /// NPC总数
    pub npc_zongshu: Option<i32>,
    /// 图片数量
    pub tupian_shuliang: Option<i32>,
    /// 信息标题
    pub xinxi_biaoti: Option<String>,
    /// 副标题
    pub fu_biaoti: Option<String>,
    /// 主标题
    pub zhu_biaoti: Option<String>,
    /// 背景图片
    pub beijing_tupian: Option<String>,
    /// 音乐文件
    pub yinyue_wenjian: Option<String>,
    /// 日期
    pub riqi: Option<String>,
    /// 主分类
    pub zhu_fenlei: Option<String>,
    /// 所有分类
    pub suoyou_fenlei: Option<String>,
    /// 列表分类
    pub liebiao_fenlei: Option<String>,
    /// 基础信息YAML
    pub jichuxinxi_yaml: Option<String>,
    /// 怪物列表YAML
    pub guaiwu_liebiao_yaml: Option<String>,
    /// NPC列表YAML
    pub npc_liebiao_yaml: Option<String>,
    /// 地图图片YAML
    pub ditu_tupian_yaml: Option<String>,
    /// 本地图片路径YAML
    pub benditupian_lujing_yaml: Option<String>,
    /// 有基础信息
    pub you_jichuxinxi: Option<String>,
    /// 有怪物信息
    pub you_guaiwuxinxi: Option<String>,
    /// 有NPC信息
    pub you_npcxinxi: Option<String>,
    /// 汇总日期
    pub huizong_riqi: Option<String>,
}

/// 完整地图信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_wanzheng_xinxi {
    /// 地图ID
    pub id: String,
    /// 基础信息（来自ditu_name表）
    pub jiben_xinxi: Option<ditu_jiben_xinxi>,
    /// 汇总信息（来自ditu_huizong表）
    pub huizong_xinxi: Option<ditu_huizong_xinxi>,
}

/// 地图查询结果结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_chaxun_jieguo {
    /// 查询是否成功
    pub chenggong: bool,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 完整地图信息（全部信息查询时使用）
    pub wanzheng_xinxi: Option<ditu_wanzheng_xinxi>,
    /// 地图数据（指定字段查询时使用）
    pub ditu_shuju: Option<HashMap<String, String>>,
}

/// 地图列表项结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_liebiao_xiang {
    /// 地图ID
    pub ditu_id: String,
    /// 地图名称（优先使用name表的name，如果没有则使用huizong表的ditu_mingcheng）
    pub ditu_mingcheng: String,
    /// 显示名称
    pub xianshi_mingcheng: Option<String>,
    /// 主分类
    pub zhu_fenlei: Option<String>,
}

/// 地图列表分页参数结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_liebiao_fenye_canshu {
    /// 每页数量
    pub meiye_shuliang: u32,
    /// 当前页数（从1开始）
    pub dangqian_ye: u32,
}

/// 地图列表分页信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_liebiao_fenye_xinxi {
    /// 当前页数
    pub dangqian_ye: u32,
    /// 总页数
    pub zongyeshu: u32,
    /// 总地图数
    pub zong_ditu_shu: u64,
}

/// 地图列表查询结果结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_liebiao_jieguo {
    /// 查询是否成功
    pub chenggong: bool,
    /// 错误信息
    pub cuowu_xinxi: Option<String>,
    /// 地图列表
    pub ditu_liebiao: Vec<ditu_liebiao_xiang>,
    /// 分页信息
    pub fenye_xinxi: ditu_liebiao_fenye_xinxi,
}

/// 地图缓存统计信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_huancun_tongji {
    /// 是否启用Redis缓存
    pub qiyong_redis: bool,
    /// 地图全部信息缓存数量
    pub quanbu_xinxi_shuliang: u64,
    /// 地图列表缓存数量
    pub liebiao_shuliang: u64,
    /// 统计信息文本
    pub tongji_xinxi: String,
}
