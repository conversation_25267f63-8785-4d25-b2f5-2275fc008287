#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::ditu_redis_kongzhi::ditu_redis_kongzhi;
use super::ditu_rizhi_kongzhi::ditu_zifuchuan_changliangguanli;
use super::ditu_sql_kongzhi::ditu_sql_guanli;
use super::ditushujujiegouti::{
    ditu_liebiao_fenye_canshu, ditu_liebiao_fenye_xinxi,
    ditu_liebiao_jieguo, ditu_liebiao_xiang,
};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use sqlx::Row;
use std::collections::HashMap;

/// 地图列表数据管理器
pub struct ditu_liebiao_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_kongzhi: Option<ditu_redis_kongzhi>,
}

impl ditu_liebiao_guanli {
    /// 创建新的地图列表管理器实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_kongzhi: None,
        }
    }

    /// 创建带Redis缓存的地图列表管理器实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_kongzhi: ditu_redis_kongzhi) -> Self {
        Self {
            mysql_lianjie,
            redis_kongzhi: Some(redis_kongzhi),
        }
    }

    /// 获取地图列表（带Redis缓存）
    pub async fn huoqu_ditu_liebiao(&self, fenye_canshu: ditu_liebiao_fenye_canshu) -> anyhow::Result<ditu_liebiao_jieguo> {
        // 参数验证
        if fenye_canshu.meiye_shuliang == 0 || fenye_canshu.dangqian_ye == 0 {
            return Ok(ditu_liebiao_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(ditu_zifuchuan_changliangguanli::cuowu_canshu_wuxiao.to_string()),
                ditu_liebiao: Vec::new(),
                fenye_xinxi: ditu_liebiao_fenye_xinxi {
                    dangqian_ye: fenye_canshu.dangqian_ye,
                    zongyeshu: 0,
                    zong_ditu_shu: 0,
                },
            });
        }

        // 尝试从Redis获取缓存
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            if let Ok(Some(huancun_jieguo)) = redis_kongzhi
                .huoqu_ditu_liebiao(fenye_canshu.meiye_shuliang, fenye_canshu.dangqian_ye)
                .await
            {
                return Ok(huancun_jieguo);
            }
        }

        // 从数据库获取数据
        let sql_guanli = ditu_sql_guanli::new(self.mysql_lianjie.clone());

        // 获取总数
        let zong_ditu_shu = match sql_guanli.huoqu_ditu_zongshu().await {
            Ok(zongshu) => zongshu,
            Err(e) => {
                return Ok(ditu_liebiao_jieguo {
                    chenggong: false,
                    cuowu_xinxi: Some(format!("获取地图总数失败: {}", e)),
                    ditu_liebiao: Vec::new(),
                    fenye_xinxi: ditu_liebiao_fenye_xinxi {
                        dangqian_ye: fenye_canshu.dangqian_ye,
                        zongyeshu: 0,
                        zong_ditu_shu: 0,
                    },
                });
            }
        };

        // 计算总页数
        let zongyeshu = if zong_ditu_shu == 0 {
            0
        } else {
            ((zong_ditu_shu as f64) / (fenye_canshu.meiye_shuliang as f64)).ceil() as u32
        };

        // 检查页数是否超出范围
        if fenye_canshu.dangqian_ye > zongyeshu && zongyeshu > 0 {
            return Ok(ditu_liebiao_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(ditu_zifuchuan_changliangguanli::shengcheng_cuowu_yeshu_chaochufanwei(
                    fenye_canshu.dangqian_ye,
                    zongyeshu,
                )),
                ditu_liebiao: Vec::new(),
                fenye_xinxi: ditu_liebiao_fenye_xinxi {
                    dangqian_ye: fenye_canshu.dangqian_ye,
                    zongyeshu,
                    zong_ditu_shu,
                },
            });
        }

        // 获取地图列表
        let ditu_liebiao = match sql_guanli
            .huoqu_ditu_liebiao(fenye_canshu.meiye_shuliang, fenye_canshu.dangqian_ye)
            .await
        {
            Ok(liebiao) => liebiao,
            Err(e) => {
                return Ok(ditu_liebiao_jieguo {
                    chenggong: false,
                    cuowu_xinxi: Some(format!("获取地图列表失败: {}", e)),
                    ditu_liebiao: Vec::new(),
                    fenye_xinxi: ditu_liebiao_fenye_xinxi {
                        dangqian_ye: fenye_canshu.dangqian_ye,
                        zongyeshu,
                        zong_ditu_shu,
                    },
                });
            }
        };

        let jieguo = ditu_liebiao_jieguo {
            chenggong: true,
            cuowu_xinxi: None,
            ditu_liebiao,
            fenye_xinxi: ditu_liebiao_fenye_xinxi {
                dangqian_ye: fenye_canshu.dangqian_ye,
                zongyeshu,
                zong_ditu_shu,
            },
        };

        // 缓存到Redis
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            let _ = redis_kongzhi
                .cunchu_ditu_liebiao(
                    fenye_canshu.meiye_shuliang,
                    fenye_canshu.dangqian_ye,
                    &jieguo,
                )
                .await;
        }

        Ok(jieguo)
    }

    /// 获取地图缓存统计信息
    pub async fn huoqu_ditu_huancun_tongji(&self) -> anyhow::Result<String> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => {
                // 获取地图全部信息缓存统计
                let quanbu_tongji = redis_kongzhi.huoqu_ditu_huancun_tongji().await?;
                
                // 获取地图列表缓存统计
                let liebiao_moshi = ditu_zifuchuan_changliangguanli::redis_jian_moshi_ditu_liebiao;
                let liebiao_shuliang = redis_kongzhi.redis_lianjie.count_keys_by_pattern(liebiao_moshi).await?;
                let liebiao_tongji = ditu_zifuchuan_changliangguanli::shengcheng_tongji_ditu_liebiao_huancun(liebiao_shuliang);

                Ok(format!("{}\n{}", quanbu_tongji, liebiao_tongji))
            }
            None => Ok(ditu_zifuchuan_changliangguanli::tongji_wei_qiyong_redis_huancun.to_string()),
        }
    }

    /// 清理地图相关的所有Redis缓存
    pub async fn qingchu_suoyou_ditu_huancun(&self) -> anyhow::Result<(u64, u64)> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => {
                let quanbu_shanchu = redis_kongzhi.qingchu_ditu_quanbu_xinxi_huancun().await?;
                let liebiao_shanchu = redis_kongzhi.qingchu_ditu_liebiao_huancun().await?;
                Ok((quanbu_shanchu, liebiao_shanchu))
            }
            None => Ok((0, 0)),
        }
    }

    /// 搜索地图（按名称模糊搜索）
    pub async fn sousuo_ditu(&self, guanjianci: &str, fenye_canshu: ditu_liebiao_fenye_canshu) -> anyhow::Result<ditu_liebiao_jieguo> {
        // 参数验证
        if fenye_canshu.meiye_shuliang == 0 || fenye_canshu.dangqian_ye == 0 {
            return Ok(ditu_liebiao_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(ditu_zifuchuan_changliangguanli::cuowu_canshu_wuxiao.to_string()),
                ditu_liebiao: Vec::new(),
                fenye_xinxi: ditu_liebiao_fenye_xinxi {
                    dangqian_ye: fenye_canshu.dangqian_ye,
                    zongyeshu: 0,
                    zong_ditu_shu: 0,
                },
            });
        }

        let sousuo_moshi = format!("%{}%", guanjianci);
        let pianyi = (fenye_canshu.dangqian_ye - 1) * fenye_canshu.meiye_shuliang;

        // 获取搜索结果总数
        let zongshu_sql = r#"
            SELECT COUNT(*) as count
            FROM ditu_name n
            LEFT JOIN ditu_huizong h ON n.id = h.ditu_id
            WHERE n.name LIKE ? OR h.ditu_mingcheng LIKE ? OR h.xianshi_mingcheng LIKE ?
        "#;

        let zong_ditu_shu = match sqlx::query(zongshu_sql)
            .bind(&sousuo_moshi)
            .bind(&sousuo_moshi)
            .bind(&sousuo_moshi)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(row) => {
                let count: i64 = row.get("count");
                count as u64
            }
            Err(e) => {
                return Ok(ditu_liebiao_jieguo {
                    chenggong: false,
                    cuowu_xinxi: Some(format!("搜索地图总数失败: {}", e)),
                    ditu_liebiao: Vec::new(),
                    fenye_xinxi: ditu_liebiao_fenye_xinxi {
                        dangqian_ye: fenye_canshu.dangqian_ye,
                        zongyeshu: 0,
                        zong_ditu_shu: 0,
                    },
                });
            }
        };

        // 计算总页数
        let zongyeshu = if zong_ditu_shu == 0 {
            0
        } else {
            ((zong_ditu_shu as f64) / (fenye_canshu.meiye_shuliang as f64)).ceil() as u32
        };

        // 检查页数是否超出范围
        if fenye_canshu.dangqian_ye > zongyeshu && zongyeshu > 0 {
            return Ok(ditu_liebiao_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(ditu_zifuchuan_changliangguanli::shengcheng_cuowu_yeshu_chaochufanwei(
                    fenye_canshu.dangqian_ye,
                    zongyeshu,
                )),
                ditu_liebiao: Vec::new(),
                fenye_xinxi: ditu_liebiao_fenye_xinxi {
                    dangqian_ye: fenye_canshu.dangqian_ye,
                    zongyeshu,
                    zong_ditu_shu,
                },
            });
        }

        // 获取搜索结果
        let sousuo_sql = r#"
            SELECT 
                n.id as ditu_id,
                COALESCE(n.name, h.ditu_mingcheng, '') as ditu_mingcheng,
                h.xianshi_mingcheng,
                h.zhu_fenlei
            FROM ditu_name n
            LEFT JOIN ditu_huizong h ON n.id = h.ditu_id
            WHERE n.name LIKE ? OR h.ditu_mingcheng LIKE ? OR h.xianshi_mingcheng LIKE ?
            ORDER BY n.id
            LIMIT ? OFFSET ?
        "#;

        let ditu_liebiao = match sqlx::query(sousuo_sql)
            .bind(&sousuo_moshi)
            .bind(&sousuo_moshi)
            .bind(&sousuo_moshi)
            .bind(fenye_canshu.meiye_shuliang)
            .bind(pianyi)
            .fetch_all(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await
        {
            Ok(rows) => {
                let mut liebiao = Vec::new();
                for row in rows {
                    let liebiao_xiang = ditu_liebiao_xiang {
                        ditu_id: row.get::<Option<String>, _>("ditu_id").unwrap_or_default(),
                        ditu_mingcheng: row.get::<String, _>("ditu_mingcheng"),
                        xianshi_mingcheng: row.get("xianshi_mingcheng"),
                        zhu_fenlei: row.get("zhu_fenlei"),
                    };
                    liebiao.push(liebiao_xiang);
                }
                liebiao
            }
            Err(e) => {
                return Ok(ditu_liebiao_jieguo {
                    chenggong: false,
                    cuowu_xinxi: Some(format!("搜索地图列表失败: {}", e)),
                    ditu_liebiao: Vec::new(),
                    fenye_xinxi: ditu_liebiao_fenye_xinxi {
                        dangqian_ye: fenye_canshu.dangqian_ye,
                        zongyeshu,
                        zong_ditu_shu,
                    },
                });
            }
        };

        Ok(ditu_liebiao_jieguo {
            chenggong: true,
            cuowu_xinxi: None,
            ditu_liebiao,
            fenye_xinxi: ditu_liebiao_fenye_xinxi {
                dangqian_ye: fenye_canshu.dangqian_ye,
                zongyeshu,
                zong_ditu_shu,
            },
        })
    }
}
