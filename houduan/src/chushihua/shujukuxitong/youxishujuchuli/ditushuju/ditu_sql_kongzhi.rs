#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::ditu_rizhi_kongzhi::ditu_zifuchuan_changliangguanli;
use super::ditushujujiegouti::{
    ditu_huizong_xinxi, ditu_jiben_xinxi, ditu_liebiao_xiang, ditu_wanzheng_xinxi,
};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use anyhow::{anyhow, Result};
use sqlx::Row;
use std::collections::HashMap;

/// 地图SQL数据管理器
pub struct ditu_sql_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
}

impl ditu_sql_guanli {
    /// 创建新的地图SQL管理器实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self { mysql_lianjie }
    }

    /// 检查地图是否存在于ditu_name表中
    pub async fn jiancha_ditu_cunzai(&self, ditu_id: &str) -> Result<bool> {
        let sql = "SELECT COUNT(*) as count FROM ditu_name WHERE id = ?";
        let row = sqlx::query(sql)
            .bind(ditu_id)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let count: i64 = row.get("count");
        Ok(count > 0)
    }

    /// 从ditu_name表获取地图基础信息
    pub async fn huoqu_ditu_jiben_xinxi(&self, ditu_id: &str) -> Result<Option<ditu_jiben_xinxi>> {
        let sql = "SELECT id, name FROM ditu_name WHERE id = ?";
        let row_result = sqlx::query(sql)
            .bind(ditu_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match row_result {
            Some(row) => {
                let jiben_xinxi = ditu_jiben_xinxi {
                    id: row.get("id"),
                    name: row.get("name"),
                };
                Ok(Some(jiben_xinxi))
            }
            None => Ok(None),
        }
    }

    /// 从ditu_huizong表获取地图汇总信息
    pub async fn huoqu_ditu_huizong_xinxi(&self, ditu_id: &str) -> Result<Option<ditu_huizong_xinxi>> {
        let sql = "SELECT * FROM ditu_huizong WHERE ditu_id = ?";
        let row_result = sqlx::query(sql)
            .bind(ditu_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match row_result {
            Some(row) => {
                let huizong_xinxi = ditu_huizong_xinxi {
                    ditu_id: row.get("ditu_id"),
                    ditu_mingcheng: row.get("ditu_mingcheng"),
                    xianshi_mingcheng: row.get("xianshi_mingcheng"),
                    leixing_town: row.get("leixing_town"),
                    leixing_field: row.get("leixing_field"),
                    leixing_dungeon: row.get("leixing_dungeon"),
                    leixing_quest: row.get("leixing_quest"),
                    leixing_instance: row.get("leixing_instance"),
                    leixing_siege: row.get("leixing_siege"),
                    leixing_pvp: row.get("leixing_pvp"),
                    leixing_other: row.get("leixing_other"),
                    leixing_weizhi: row.get("leixing_weizhi"),
                    fenlei_town: row.get("fenlei_town"),
                    fenlei_field: row.get("fenlei_field"),
                    fenlei_dungeon: row.get("fenlei_dungeon"),
                    fenlei_quest: row.get("fenlei_quest"),
                    fenlei_instance: row.get("fenlei_instance"),
                    fenlei_siege: row.get("fenlei_siege"),
                    fenlei_pvp: row.get("fenlei_pvp"),
                    fenlei_other: row.get("fenlei_other"),
                    fenlei_weizhi: row.get("fenlei_weizhi"),
                    you_guaiwu: row.get("you_guaiwu"),
                    you_npc: row.get("you_npc"),
                    you_yinyue: row.get("you_yinyue"),
                    you_tupian: row.get("you_tupian"),
                    tongzhi_jinru: row.get("tongzhi_jinru"),
                    guaiwu_zongshu: row.get("guaiwu_zongshu"),
                    npc_zongshu: row.get("npc_zongshu"),
                    tupian_shuliang: row.get("tupian_shuliang"),
                    xinxi_biaoti: row.get("xinxi_biaoti"),
                    fu_biaoti: row.get("fu_biaoti"),
                    zhu_biaoti: row.get("zhu_biaoti"),
                    beijing_tupian: row.get("beijing_tupian"),
                    yinyue_wenjian: row.get("yinyue_wenjian"),
                    riqi: row.get("riqi"),
                    zhu_fenlei: row.get("zhu_fenlei"),
                    suoyou_fenlei: row.get("suoyou_fenlei"),
                    liebiao_fenlei: row.get("liebiao_fenlei"),
                    jichuxinxi_yaml: row.get("jichuxinxi_yaml"),
                    guaiwu_liebiao_yaml: row.get("guaiwu_liebiao_yaml"),
                    npc_liebiao_yaml: row.get("npc_liebiao_yaml"),
                    ditu_tupian_yaml: row.get("ditu_tupian_yaml"),
                    benditupian_lujing_yaml: row.get("benditupian_lujing_yaml"),
                    you_jichuxinxi: row.get("you_jichuxinxi"),
                    you_guaiwuxinxi: row.get("you_guaiwuxinxi"),
                    you_npcxinxi: row.get("you_npcxinxi"),
                    huizong_riqi: row.get("huizong_riqi"),
                };
                Ok(Some(huizong_xinxi))
            }
            None => Ok(None),
        }
    }

    /// 获取地图完整信息
    pub async fn huoqu_ditu_wanzheng_xinxi(&self, ditu_id: &str) -> Result<ditu_wanzheng_xinxi> {
        // 检查地图是否存在
        if !self.jiancha_ditu_cunzai(ditu_id).await? {
            return Err(anyhow!(
                ditu_zifuchuan_changliangguanli::shengcheng_cuowu_ditu_bucunzai(ditu_id)
            ));
        }

        // 获取基础信息
        let jiben_xinxi = self.huoqu_ditu_jiben_xinxi(ditu_id).await?;

        // 获取汇总信息
        let huizong_xinxi = self.huoqu_ditu_huizong_xinxi(ditu_id).await?;

        Ok(ditu_wanzheng_xinxi {
            id: ditu_id.to_string(),
            jiben_xinxi,
            huizong_xinxi,
        })
    }

    /// 获取地图指定字段数据
    pub async fn huoqu_ditu_zhiding_ziduan(&self, ditu_id: &str, ziduan_liebiao: Vec<String>) -> Result<HashMap<String, String>> {
        // 检查地图是否存在
        if !self.jiancha_ditu_cunzai(ditu_id).await? {
            return Err(anyhow!(
                ditu_zifuchuan_changliangguanli::shengcheng_cuowu_ditu_bucunzai(ditu_id)
            ));
        }

        let mut jieguo = HashMap::new();

        // 分离ditu_name表和ditu_huizong表的字段
        let ditu_name_ziduan = vec!["id", "name"];
        let mut name_ziduan = Vec::new();
        let mut huizong_ziduan = Vec::new();

        for ziduan in &ziduan_liebiao {
            if ditu_name_ziduan.contains(&ziduan.as_str()) {
                name_ziduan.push(ziduan.clone());
            } else {
                huizong_ziduan.push(ziduan.clone());
            }
        }

        // 查询ditu_name表字段
        if !name_ziduan.is_empty() {
            let ziduan_str = name_ziduan.join(", ");
            let sql = format!("SELECT {} FROM ditu_name WHERE id = ?", ziduan_str);
            
            if let Some(row) = sqlx::query(&sql)
                .bind(ditu_id)
                .fetch_optional(&self.mysql_lianjie.lianjie_chi)
                .await? {
                
                for ziduan in &name_ziduan {
                    if let Ok(zhi) = row.try_get::<Option<String>, _>(ziduan.as_str()) {
                        jieguo.insert(ziduan.clone(), zhi.unwrap_or_default());
                    }
                }
            }
        }

        // 查询ditu_huizong表字段
        if !huizong_ziduan.is_empty() {
            let ziduan_str = huizong_ziduan.join(", ");
            let sql = format!("SELECT {} FROM ditu_huizong WHERE ditu_id = ?", ziduan_str);
            
            if let Some(row) = sqlx::query(&sql)
                .bind(ditu_id)
                .fetch_optional(&self.mysql_lianjie.lianjie_chi)
                .await? {
                
                for ziduan in &huizong_ziduan {
                    if let Ok(zhi) = row.try_get::<Option<String>, _>(ziduan.as_str()) {
                        jieguo.insert(ziduan.clone(), zhi.unwrap_or_default());
                    } else if let Ok(zhi) = row.try_get::<Option<i32>, _>(ziduan.as_str()) {
                        jieguo.insert(ziduan.clone(), zhi.map_or(String::new(), |v| v.to_string()));
                    }
                }
            }
        }

        Ok(jieguo)
    }

    /// 获取地图总数
    pub async fn huoqu_ditu_zongshu(&self) -> Result<u64> {
        let sql = "SELECT COUNT(*) as count FROM ditu_name";
        let row = sqlx::query(sql)
            .fetch_one(&self.mysql_lianjie.lianjie_chi)
            .await?;

        let count: i64 = row.get("count");
        Ok(count as u64)
    }

    /// 获取地图列表（分页）
    pub async fn huoqu_ditu_liebiao(&self, meiye_shuliang: u32, dangqian_ye: u32) -> Result<Vec<ditu_liebiao_xiang>> {
        let pianyi = (dangqian_ye - 1) * meiye_shuliang;
        
        let sql = r#"
            SELECT 
                n.id as ditu_id,
                COALESCE(n.name, h.ditu_mingcheng, '') as ditu_mingcheng,
                h.xianshi_mingcheng,
                h.zhu_fenlei
            FROM ditu_name n
            LEFT JOIN ditu_huizong h ON n.id = h.ditu_id
            ORDER BY n.id
            LIMIT ? OFFSET ?
        "#;

        let rows = sqlx::query(sql)
            .bind(meiye_shuliang)
            .bind(pianyi)
            .fetch_all(&self.mysql_lianjie.lianjie_chi)
            .await?;

        let mut liebiao = Vec::new();
        for row in rows {
            let liebiao_xiang = ditu_liebiao_xiang {
                ditu_id: row.get::<Option<String>, _>("ditu_id").unwrap_or_default(),
                ditu_mingcheng: row.get::<String, _>("ditu_mingcheng"),
                xianshi_mingcheng: row.get("xianshi_mingcheng"),
                zhu_fenlei: row.get("zhu_fenlei"),
            };
            liebiao.push(liebiao_xiang);
        }

        Ok(liebiao)
    }
}
