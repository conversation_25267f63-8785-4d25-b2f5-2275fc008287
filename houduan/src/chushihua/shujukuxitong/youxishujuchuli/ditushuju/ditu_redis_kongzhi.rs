#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::ditu_rizhi_kongzhi::ditu_zifuchuan_changliangguanli;
use super::ditushujujiegouti::{ditu_liebiao_jieguo, ditu_wanzheng_xinxi};
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;
use anyhow::Result;

/// 地图Redis缓存控制类
pub struct ditu_redis_kongzhi {
    pub redis_lianjie: redis_lianjie_guanli,
}

impl ditu_redis_kongzhi {
    /// 创建新的地图Redis控制实例
    pub fn new(redis_lianjie: redis_lianjie_guanli) -> Self {
        Self {
            redis_lianjie,
        }
    }

    /// 生成地图全部信息的Redis键名
    fn shengcheng_quanbu_xinxi_jian(&self, ditu_id: &str) -> String {
        ditu_zifuchuan_changliangguanli::sheng<PERSON>_redis_jian_ditu_quanbu(ditu_id)
    }

    /// 从Redis获取地图全部信息
    pub async fn huoqu_quanbu_xinxi(&self, ditu_id: &str) -> Result<Option<ditu_wanzheng_xinxi>> {
        let jian = self.shengcheng_quanbu_xinxi_jian(ditu_id);

        match self.redis_lianjie.huoqu(&jian).await? {
            Some(json_str) => {
                match serde_json::from_str::<ditu_wanzheng_xinxi>(&json_str) {
                    Ok(ditu_xinxi) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &ditu_zifuchuan_changliangguanli::rizhi_redis_huoqu_chenggong_quanbu.replace("{}", ditu_id),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Ok(Some(ditu_xinxi))
                    }
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &format!("地图{}的Redis缓存JSON解析失败: {}", ditu_id, e),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Ok(None)
                    }
                }
            }
            None => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &ditu_zifuchuan_changliangguanli::rizhi_redis_wu_huancun_quanbu.replace("{}", ditu_id),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(None)
            }
        }
    }

    /// 将地图全部信息存储到Redis（3天缓存）
    pub async fn cunchu_quanbu_xinxi(&self, ditu_id: &str, ditu_xinxi: &ditu_wanzheng_xinxi) -> Result<()> {
        let jian = self.shengcheng_quanbu_xinxi_jian(ditu_id);
        let json_str = serde_json::to_string(ditu_xinxi)?;

        match self.redis_lianjie.shezhi_with_guoqi(
            &jian,
            &json_str,
            ditu_zifuchuan_changliangguanli::ditu_quanbu_xinxi_huancun_shijian as i64,
        ).await {
            Ok(_) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &ditu_zifuchuan_changliangguanli::rizhi_redis_cunchu_chenggong_quanbu.replace("{}", ditu_id),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(())
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("地图{}的全部信息缓存到Redis失败: {}", ditu_id, e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(e)
            }
        }
    }

    /// 删除指定地图的全部信息缓存
    pub async fn shanchu_quanbu_xinxi(&self, ditu_id: &str) -> Result<bool> {
        let jian = self.shengcheng_quanbu_xinxi_jian(ditu_id);

        match self.redis_lianjie.shanchu(&jian).await {
            Ok(shanchu_chenggong) => {
                if shanchu_chenggong {
                    crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                        &ditu_zifuchuan_changliangguanli::rizhi_redis_shanchu_chenggong_quanbu.replace("{}", ditu_id),
                        crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                    );
                    Ok(true)
                } else {
                    crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                        &ditu_zifuchuan_changliangguanli::rizhi_redis_wuxu_shanchu_quanbu.replace("{}", ditu_id),
                        crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                    );
                    Ok(false)
                }
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &format!("删除地图{}的Redis缓存失败: {}", ditu_id, e),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(e)
            }
        }
    }

    /// 清理地图全部数据获取的Redis缓存
    /// 只清除地图数据获取相关的缓存，不会清理其他缓存
    pub async fn qingchu_ditu_quanbu_xinxi_huancun(&self) -> Result<u64> {
        let moshi = ditu_zifuchuan_changliangguanli::redis_jian_moshi_ditu_quanbu;

        match self.redis_lianjie.shanchu_by_pattern(moshi).await {
            Ok(shanchu_shuliang) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &ditu_zifuchuan_changliangguanli::rizhi_redis_qingchu_chenggong_quanbu.replace("{}", &shanchu_shuliang.to_string()),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(shanchu_shuliang)
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &ditu_zifuchuan_changliangguanli::rizhi_redis_qingchu_shibai_quanbu.replace("{}", &e.to_string()),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(e)
            }
        }
    }

    /// 获取地图缓存统计信息
    pub async fn huoqu_ditu_huancun_tongji(&self) -> Result<String> {
        let moshi = ditu_zifuchuan_changliangguanli::redis_jian_moshi_ditu_quanbu;

        match self.redis_lianjie.count_keys_by_pattern(moshi).await {
            Ok(shuliang) => {
                let tongji_xinxi = ditu_zifuchuan_changliangguanli::shengcheng_tongji_ditu_quanbu_huancun(shuliang);
                Ok(tongji_xinxi)
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &ditu_zifuchuan_changliangguanli::rizhi_redis_tongji_shibai.replace("{}", &e.to_string()),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(e)
            }
        }
    }

    // ==================== 地图列表缓存管理 ====================

    /// 生成地图列表的Redis键名
    fn shengcheng_liebiao_jian(&self, meiye_shuliang: u32, dangqian_ye: u32) -> String {
        ditu_zifuchuan_changliangguanli::shengcheng_redis_jian_ditu_liebiao(meiye_shuliang, dangqian_ye)
    }

    /// 从Redis获取地图列表
    pub async fn huoqu_ditu_liebiao(&self, meiye_shuliang: u32, dangqian_ye: u32) -> Result<Option<ditu_liebiao_jieguo>> {
        let jian = self.shengcheng_liebiao_jian(meiye_shuliang, dangqian_ye);

        match self.redis_lianjie.huoqu(&jian).await? {
            Some(json_str) => {
                match serde_json::from_str::<ditu_liebiao_jieguo>(&json_str) {
                    Ok(liebiao_jieguo) => {
                        crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                            &format!("从Redis成功获取地图列表，每页{}个，第{}页", meiye_shuliang, dangqian_ye),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Ok(Some(liebiao_jieguo))
                    }
                    Err(e) => {
                        crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                            &ditu_zifuchuan_changliangguanli::rizhi_redis_json_jiexi_shibai_liebiao.replace("{}", &e.to_string()),
                            crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                        );
                        Ok(None)
                    }
                }
            }
            None => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("地图列表在Redis中无缓存，每页{}个，第{}页", meiye_shuliang, dangqian_ye),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(None)
            }
        }
    }

    /// 将地图列表存储到Redis（5小时缓存）
    pub async fn cunchu_ditu_liebiao(&self, meiye_shuliang: u32, dangqian_ye: u32, liebiao_jieguo: &ditu_liebiao_jieguo) -> Result<()> {
        let jian = self.shengcheng_liebiao_jian(meiye_shuliang, dangqian_ye);
        let json_str = serde_json::to_string(liebiao_jieguo)?;

        match self.redis_lianjie.shezhi_with_guoqi(
            &jian,
            &json_str,
            ditu_zifuchuan_changliangguanli::ditu_liebiao_huancun_shijian as i64,
        ).await {
            Ok(_) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &format!("地图列表已缓存到Redis，每页{}个，第{}页，有效期5小时", meiye_shuliang, dangqian_ye),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(())
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &ditu_zifuchuan_changliangguanli::rizhi_redis_cunchu_shibai_liebiao.replace("{}", &e.to_string()),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(e)
            }
        }
    }

    /// 清理地图列表的Redis缓存
    /// 只清除地图列表相关的缓存，不会清理其他缓存
    pub async fn qingchu_ditu_liebiao_huancun(&self) -> Result<u64> {
        let moshi = ditu_zifuchuan_changliangguanli::redis_jian_moshi_ditu_liebiao;

        match self.redis_lianjie.shanchu_by_pattern(moshi).await {
            Ok(shanchu_shuliang) => {
                crate::rizhixitong::rizhixitong_xinxi_with_moshi(
                    &ditu_zifuchuan_changliangguanli::rizhi_redis_qingchu_chenggong_liebiao.replace("{}", &shanchu_shuliang.to_string()),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Ok(shanchu_shuliang)
            }
            Err(e) => {
                crate::rizhixitong::rizhixitong_cuowu_with_moshi(
                    &ditu_zifuchuan_changliangguanli::rizhi_redis_qingchu_shibai_liebiao.replace("{}", &e.to_string()),
                    crate::rizhixitong::rizhixitong_shuchu_moshi::xiangxi,
                );
                Err(e)
            }
        }
    }
}
