#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::ditu_redis_kongzhi::ditu_redis_kongzhi;
use super::ditu_rizhi_kongzhi::ditu_zifuchuan_changliangguanli;
use super::ditu_sql_kongzhi::ditu_sql_guanli;
use super::ditushujujiegouti::{ditu_chaxun_jieguo, ditu_wanzheng_xinxi};
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use anyhow::Result;
use std::collections::HashMap;

/// 地图数据处理器
pub struct ditu_shuju_chuliqui {
    sql_guanli: ditu_sql_guanli,
    redis_kongzhi: Option<ditu_redis_kongzhi>,
}

impl ditu_shuju_chuliqui {
    /// 创建新的地图数据处理器实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            sql_guanli: ditu_sql_guanli::new(mysql_lianjie),
            redis_kongzhi: None,
        }
    }

    /// 创建带Redis缓存的地图数据处理器实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_kongzhi: ditu_redis_kongzhi) -> Self {
        Self {
            sql_guanli: ditu_sql_guanli::new(mysql_lianjie),
            redis_kongzhi: Some(redis_kongzhi),
        }
    }

    /// 获取地图全部信息（带Redis缓存）
    pub async fn huoqu_ditu_quanbu_xinxi(&self, ditu_id: &str) -> Result<ditu_chaxun_jieguo> {
        // 尝试从Redis获取缓存
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            if let Ok(Some(huancun_xinxi)) = redis_kongzhi.huoqu_quanbu_xinxi(ditu_id).await {
                return Ok(ditu_chaxun_jieguo {
                    chenggong: true,
                    cuowu_xinxi: None,
                    wanzheng_xinxi: Some(huancun_xinxi),
                    ditu_shuju: None,
                });
            }
        }

        // 从数据库获取数据
        match self.sql_guanli.huoqu_ditu_wanzheng_xinxi(ditu_id).await {
            Ok(wanzheng_xinxi) => {
                // 缓存到Redis
                if let Some(redis_kongzhi) = &self.redis_kongzhi {
                    let _ = redis_kongzhi.cunchu_quanbu_xinxi(ditu_id, &wanzheng_xinxi).await;
                }

                Ok(ditu_chaxun_jieguo {
                    chenggong: true,
                    cuowu_xinxi: None,
                    wanzheng_xinxi: Some(wanzheng_xinxi),
                    ditu_shuju: None,
                })
            }
            Err(e) => Ok(ditu_chaxun_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(e.to_string()),
                wanzheng_xinxi: None,
                ditu_shuju: None,
            }),
        }
    }

    /// 获取地图指定字段数据
    pub async fn huoqu_ditu_zhiding_ziduan(&self, ditu_id: &str, ziduan_liebiao: Vec<String>) -> Result<ditu_chaxun_jieguo> {
        match self.sql_guanli.huoqu_ditu_zhiding_ziduan(ditu_id, ziduan_liebiao).await {
            Ok(ditu_shuju) => Ok(ditu_chaxun_jieguo {
                chenggong: true,
                cuowu_xinxi: None,
                wanzheng_xinxi: None,
                ditu_shuju: Some(ditu_shuju),
            }),
            Err(e) => Ok(ditu_chaxun_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(e.to_string()),
                wanzheng_xinxi: None,
                ditu_shuju: None,
            }),
        }
    }

    /// 根据查询模式获取地图数据
    pub async fn huoqu_ditu_shuju(&self, ditu_id: &str, chaxun_moshi: &str, ziduan_liebiao: Option<Vec<String>>) -> Result<ditu_chaxun_jieguo> {
        match chaxun_moshi {
            moshi if moshi == ditu_zifuchuan_changliangguanli::chaxun_moshi_quanbu_xinxi => {
                self.huoqu_ditu_quanbu_xinxi(ditu_id).await
            }
            _ => {
                // 指定字段查询
                let ziduan = ziduan_liebiao.unwrap_or_default();
                if ziduan.is_empty() {
                    return Ok(ditu_chaxun_jieguo {
                        chenggong: false,
                        cuowu_xinxi: Some("指定字段查询时必须提供字段列表".to_string()),
                        wanzheng_xinxi: None,
                        ditu_shuju: None,
                    });
                }
                self.huoqu_ditu_zhiding_ziduan(ditu_id, ziduan).await
            }
        }
    }

    /// 检查地图是否存在
    pub async fn jiancha_ditu_cunzai(&self, ditu_id: &str) -> Result<bool> {
        self.sql_guanli.jiancha_ditu_cunzai(ditu_id).await
    }

    /// 删除指定地图的Redis缓存
    pub async fn shanchu_ditu_huancun(&self, ditu_id: &str) -> Result<bool> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => redis_kongzhi.shanchu_quanbu_xinxi(ditu_id).await,
            None => Ok(false),
        }
    }

    /// 清理所有地图相关的Redis缓存
    pub async fn qingchu_suoyou_ditu_huancun(&self) -> Result<u64> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => redis_kongzhi.qingchu_ditu_quanbu_xinxi_huancun().await,
            None => Ok(0),
        }
    }

    /// 获取地图缓存统计信息
    pub async fn huoqu_ditu_huancun_tongji(&self) -> Result<String> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => redis_kongzhi.huoqu_ditu_huancun_tongji().await,
            None => Ok(ditu_zifuchuan_changliangguanli::tongji_wei_qiyong_redis_huancun.to_string()),
        }
    }

    /// 批量获取地图信息
    pub async fn piliang_huoqu_ditu_xinxi(&self, ditu_id_liebiao: Vec<String>) -> Result<Vec<ditu_chaxun_jieguo>> {
        let mut jieguo_liebiao = Vec::new();

        for ditu_id in ditu_id_liebiao {
            let jieguo = self.huoqu_ditu_quanbu_xinxi(&ditu_id).await?;
            jieguo_liebiao.push(jieguo);
        }

        Ok(jieguo_liebiao)
    }

    /// 预热地图缓存（将指定地图的数据加载到Redis）
    pub async fn yure_ditu_huancun(&self, ditu_id_liebiao: Vec<String>) -> Result<(u32, u32)> {
        if self.redis_kongzhi.is_none() {
            return Ok((0, 0));
        }

        let mut chenggong_shu = 0u32;
        let mut shibai_shu = 0u32;

        for ditu_id in ditu_id_liebiao {
            match self.sql_guanli.huoqu_ditu_wanzheng_xinxi(&ditu_id).await {
                Ok(wanzheng_xinxi) => {
                    if let Some(redis_kongzhi) = &self.redis_kongzhi {
                        match redis_kongzhi.cunchu_quanbu_xinxi(&ditu_id, &wanzheng_xinxi).await {
                            Ok(_) => chenggong_shu += 1,
                            Err(_) => shibai_shu += 1,
                        }
                    }
                }
                Err(_) => shibai_shu += 1,
            }
        }

        Ok((chenggong_shu, shibai_shu))
    }

    /// 验证地图数据完整性
    pub async fn yanzheng_ditu_shuju_wanzhengxing(&self, ditu_id: &str) -> Result<HashMap<String, bool>> {
        let mut yanzheng_jieguo = HashMap::new();

        // 检查基础信息是否存在
        match self.sql_guanli.huoqu_ditu_jiben_xinxi(ditu_id).await {
            Ok(Some(_)) => {
                yanzheng_jieguo.insert("jiben_xinxi_cunzai".to_string(), true);
            }
            Ok(None) => {
                yanzheng_jieguo.insert("jiben_xinxi_cunzai".to_string(), false);
            }
            Err(_) => {
                yanzheng_jieguo.insert("jiben_xinxi_cunzai".to_string(), false);
            }
        }

        // 检查汇总信息是否存在
        match self.sql_guanli.huoqu_ditu_huizong_xinxi(ditu_id).await {
            Ok(Some(_)) => {
                yanzheng_jieguo.insert("huizong_xinxi_cunzai".to_string(), true);
            }
            Ok(None) => {
                yanzheng_jieguo.insert("huizong_xinxi_cunzai".to_string(), false);
            }
            Err(_) => {
                yanzheng_jieguo.insert("huizong_xinxi_cunzai".to_string(), false);
            }
        }

        // 检查Redis缓存是否存在
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            match redis_kongzhi.huoqu_quanbu_xinxi(ditu_id).await {
                Ok(Some(_)) => {
                    yanzheng_jieguo.insert("redis_huancun_cunzai".to_string(), true);
                }
                Ok(None) => {
                    yanzheng_jieguo.insert("redis_huancun_cunzai".to_string(), false);
                }
                Err(_) => {
                    yanzheng_jieguo.insert("redis_huancun_cunzai".to_string(), false);
                }
            }
        } else {
            yanzheng_jieguo.insert("redis_huancun_cunzai".to_string(), false);
        }

        Ok(yanzheng_jieguo)
    }

    /// 获取地图数据统计信息
    pub async fn huoqu_ditu_shuju_tongji(&self) -> Result<HashMap<String, u64>> {
        let mut tongji_jieguo = HashMap::new();

        // 获取地图总数
        match self.sql_guanli.huoqu_ditu_zongshu().await {
            Ok(zongshu) => {
                tongji_jieguo.insert("ditu_zongshu".to_string(), zongshu);
            }
            Err(_) => {
                tongji_jieguo.insert("ditu_zongshu".to_string(), 0);
            }
        }

        // 获取Redis缓存统计
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            let moshi = ditu_zifuchuan_changliangguanli::redis_jian_moshi_ditu_quanbu;
            match redis_kongzhi.redis_lianjie.count_keys_by_pattern(moshi).await {
                Ok(huancun_shu) => {
                    tongji_jieguo.insert("redis_huancun_shu".to_string(), huancun_shu);
                }
                Err(_) => {
                    tongji_jieguo.insert("redis_huancun_shu".to_string(), 0);
                }
            }
        } else {
            tongji_jieguo.insert("redis_huancun_shu".to_string(), 0);
        }

        Ok(tongji_jieguo)
    }
}
