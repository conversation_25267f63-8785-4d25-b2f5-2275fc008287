#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::ditu_redis_kongzhi::ditu_redis_kongzhi;
use super::ditu_rizhi_kongzhi::ditu_zifuchuan_changliangguanli;
use super::ditushujuchuli::ditu_shuju_chuliqui;
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;

/// 地图数据处理功能测试
pub struct ditu_ceshi;

impl ditu_ceshi {
    /// 测试地图全部信息获取功能
    pub async fn ceshi_huoqu_quanbu_xinxi() {
        println!("=== 开始测试地图全部信息获取功能 ===");

        // 创建MySQL连接
        let mysql_lianjie = mysql_lianjie_guanli::new();

        // 创建Redis连接（可选）
        let redis_lianjie = redis_lianjie_guanli::new();
        let redis_kongzhi = ditu_redis_kongzhi::new(redis_lianjie);
        let shuju_chuliqi = ditu_shuju_chuliqui::new_with_redis(mysql_lianjie, redis_kongzhi);

        // 测试地图ID（需要根据实际数据库中的数据调整）
        let ceshi_ditu_id = "1";

        // 第一次获取（从数据库）
        println!("第一次获取地图信息（从数据库）...");
        match shuju_chuliqi.huoqu_ditu_quanbu_xinxi(ceshi_ditu_id).await {
            Ok(jieguo) => {
                if jieguo.chenggong {
                    println!("✓ 地图信息获取成功");
                    if let Some(xinxi) = &jieguo.wanzheng_xinxi {
                        println!("  - 地图ID: {}", xinxi.id);
                        if let Some(jiben) = &xinxi.jiben_xinxi {
                            println!("  - 地图名称: {:?}", jiben.name);
                        }
                        if let Some(huizong) = &xinxi.huizong_xinxi {
                            println!("  - 显示名称: {:?}", huizong.xianshi_mingcheng);
                            println!("  - 主分类: {:?}", huizong.zhu_fenlei);
                        }
                    }
                } else {
                    println!("✗ 地图信息获取失败: {:?}", jieguo.cuowu_xinxi);
                }
            }
            Err(e) => {
                println!("✗ 地图信息获取异常: {}", e);
            }
        }

        // 第二次获取（从缓存）
        println!("\n第二次获取地图信息（从缓存）...");
        match shuju_chuliqi.huoqu_ditu_quanbu_xinxi(ceshi_ditu_id).await {
            Ok(jieguo) => {
                if jieguo.chenggong {
                    println!("✓ 地图信息获取成功（可能来自缓存）");
                } else {
                    println!("✗ 地图信息获取失败: {:?}", jieguo.cuowu_xinxi);
                }
            }
            Err(e) => {
                println!("✗ 地图信息获取异常: {}", e);
            }
        }

        println!("=== 地图全部信息获取功能测试完成 ===\n");
    }

    /// 测试地图指定字段获取功能
    pub async fn ceshi_huoqu_zhiding_ziduan() {
        println!("=== 开始测试地图指定字段获取功能 ===");

        // 创建MySQL连接
        let mysql_lianjie = mysql_lianjie_guanli::new();

        let shuju_chuliqi = ditu_shuju_chuliqui::new(mysql_lianjie);

        // 测试地图ID
        let ceshi_ditu_id = "1";
        
        // 测试字段列表
        let ziduan_liebiao = vec![
            "id".to_string(),
            "name".to_string(),
            "ditu_mingcheng".to_string(),
            "xianshi_mingcheng".to_string(),
            "zhu_fenlei".to_string(),
        ];

        // 获取指定字段
        match shuju_chuliqi.huoqu_ditu_zhiding_ziduan(ceshi_ditu_id, ziduan_liebiao).await {
            Ok(jieguo) => {
                if jieguo.chenggong {
                    println!("✓ 地图指定字段获取成功");
                    if let Some(shuju) = &jieguo.ditu_shuju {
                        for (ziduan, zhi) in shuju {
                            println!("  - {}: {}", ziduan, zhi);
                        }
                    }
                } else {
                    println!("✗ 地图指定字段获取失败: {:?}", jieguo.cuowu_xinxi);
                }
            }
            Err(e) => {
                println!("✗ 地图指定字段获取异常: {}", e);
            }
        }

        println!("=== 地图指定字段获取功能测试完成 ===\n");
    }

    /// 测试地图存在性检查功能
    pub async fn ceshi_jiancha_ditu_cunzai() {
        println!("=== 开始测试地图存在性检查功能 ===");

        // 创建MySQL连接
        let mysql_lianjie = mysql_lianjie_guanli::new();

        let shuju_chuliqi = ditu_shuju_chuliqui::new(mysql_lianjie);

        // 测试存在的地图
        let cunzai_ditu_id = "1";
        match shuju_chuliqi.jiancha_ditu_cunzai(cunzai_ditu_id).await {
            Ok(cunzai) => {
                if cunzai {
                    println!("✓ 地图 {} 存在", cunzai_ditu_id);
                } else {
                    println!("✗ 地图 {} 不存在", cunzai_ditu_id);
                }
            }
            Err(e) => {
                println!("✗ 检查地图 {} 存在性异常: {}", cunzai_ditu_id, e);
            }
        }

        // 测试不存在的地图
        let bucunzai_ditu_id = "999999";
        match shuju_chuliqi.jiancha_ditu_cunzai(bucunzai_ditu_id).await {
            Ok(cunzai) => {
                if !cunzai {
                    println!("✓ 地图 {} 确实不存在", bucunzai_ditu_id);
                } else {
                    println!("✗ 地图 {} 意外存在", bucunzai_ditu_id);
                }
            }
            Err(e) => {
                println!("✗ 检查地图 {} 存在性异常: {}", bucunzai_ditu_id, e);
            }
        }

        println!("=== 地图存在性检查功能测试完成 ===\n");
    }

    /// 测试缓存管理功能
    pub async fn ceshi_huancun_guanli() {
        println!("=== 开始测试缓存管理功能 ===");

        // 创建MySQL连接
        let mysql_lianjie = mysql_lianjie_guanli::new();

        // 创建Redis连接
        let redis_lianjie = redis_lianjie_guanli::new();
        let redis_kongzhi = ditu_redis_kongzhi::new(redis_lianjie);
        let shuju_chuliqi = ditu_shuju_chuliqui::new_with_redis(mysql_lianjie, redis_kongzhi);

        // 获取缓存统计
        println!("获取缓存统计信息...");
        match shuju_chuliqi.huoqu_ditu_huancun_tongji().await {
            Ok(tongji) => {
                println!("✓ 缓存统计获取成功");
                println!("{}", tongji);
            }
            Err(e) => {
                println!("✗ 缓存统计获取失败: {}", e);
            }
        }

        // 清理缓存
        println!("\n清理所有地图缓存...");
        match shuju_chuliqi.qingchu_suoyou_ditu_huancun().await {
            Ok(shanchu_shu) => {
                println!("✓ 缓存清理成功，删除了 {} 个缓存", shanchu_shu);
            }
            Err(e) => {
                println!("✗ 缓存清理失败: {}", e);
            }
        }

        println!("=== 缓存管理功能测试完成 ===\n");
    }

    /// 测试数据统计功能
    pub async fn ceshi_shuju_tongji() {
        println!("=== 开始测试数据统计功能 ===");

        // 创建MySQL连接
        let mysql_lianjie = mysql_lianjie_guanli::new();

        let shuju_chuliqi = ditu_shuju_chuliqui::new(mysql_lianjie);

        // 获取数据统计
        match shuju_chuliqi.huoqu_ditu_shuju_tongji().await {
            Ok(tongji) => {
                println!("✓ 数据统计获取成功");
                for (mingcheng, shuliang) in tongji {
                    println!("  - {}: {}", mingcheng, shuliang);
                }
            }
            Err(e) => {
                println!("✗ 数据统计获取失败: {}", e);
            }
        }

        println!("=== 数据统计功能测试完成 ===\n");
    }

    /// 运行所有测试
    pub async fn yunxing_suoyou_ceshi() {
        println!("开始运行地图数据处理相关功能测试...\n");

        Self::ceshi_jiancha_ditu_cunzai().await;
        Self::ceshi_huoqu_quanbu_xinxi().await;
        Self::ceshi_huoqu_zhiding_ziduan().await;
        Self::ceshi_huancun_guanli().await;
        Self::ceshi_shuju_tongji().await;

        println!("所有地图数据处理测试完成！");
    }
}
