#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use super::ditu_liebiao_shuju::ditu_liebiao_guanli;
use super::ditu_redis_kongzhi::ditu_redis_kongzhi;
use super::ditushujujiegouti::ditu_liebiao_fenye_canshu;
use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use crate::chushihua::shujukuxitong::redishujuku::redis_lianjie::redis_lianjie_guanli;

/// 地图列表功能测试
pub struct ditu_liebiao_ceshi;

impl ditu_liebiao_ceshi {
    /// 测试地图列表获取功能
    pub async fn ceshi_huoqu_ditu_liebiao() {
        println!("=== 开始测试地图列表获取功能 ===");

        // 创建MySQL连接
        let mysql_lianjie = mysql_lianjie_guanli::new();

        // 创建Redis连接（可选）
        let redis_lianjie = redis_lianjie_guanli::new();
        let redis_kongzhi = ditu_redis_kongzhi::new(redis_lianjie);
        let liebiao_guanli = ditu_liebiao_guanli::new_with_redis(mysql_lianjie, redis_kongzhi);

        // 测试参数
        let fenye_canshu = ditu_liebiao_fenye_canshu {
            meiye_shuliang: 10,
            dangqian_ye: 1,
        };

        // 执行测试
        match liebiao_guanli.huoqu_ditu_liebiao(fenye_canshu).await {
            Ok(jieguo) => {
                if jieguo.chenggong {
                    println!("✓ 地图列表获取成功");
                    println!("  - 当前页: {}", jieguo.fenye_xinxi.dangqian_ye);
                    println!("  - 总页数: {}", jieguo.fenye_xinxi.zongyeshu);
                    println!("  - 总地图数: {}", jieguo.fenye_xinxi.zong_ditu_shu);
                    println!("  - 本页地图数: {}", jieguo.ditu_liebiao.len());

                    // 显示前5个地图信息
                    for (i, ditu) in jieguo.ditu_liebiao.iter().take(5).enumerate() {
                        println!("  {}. ID: {}, 名称: {}", i + 1, ditu.ditu_id, ditu.ditu_mingcheng);
                    }
                } else {
                    println!("✗ 地图列表获取失败: {:?}", jieguo.cuowu_xinxi);
                }
            }
            Err(e) => {
                println!("✗ 地图列表获取异常: {}", e);
            }
        }

        println!("=== 地图列表获取功能测试完成 ===\n");
    }

    /// 测试地图搜索功能
    pub async fn ceshi_sousuo_ditu() {
        println!("=== 开始测试地图搜索功能 ===");

        // 创建MySQL连接
        let mysql_lianjie = mysql_lianjie_guanli::new();

        let liebiao_guanli = ditu_liebiao_guanli::new(mysql_lianjie);

        // 测试搜索关键词
        let guanjianci = "城";
        let fenye_canshu = ditu_liebiao_fenye_canshu {
            meiye_shuliang: 5,
            dangqian_ye: 1,
        };

        // 执行搜索测试
        match liebiao_guanli.sousuo_ditu(guanjianci, fenye_canshu).await {
            Ok(jieguo) => {
                if jieguo.chenggong {
                    println!("✓ 地图搜索成功");
                    println!("  - 搜索关键词: {}", guanjianci);
                    println!("  - 找到地图数: {}", jieguo.fenye_xinxi.zong_ditu_shu);
                    println!("  - 本页结果数: {}", jieguo.ditu_liebiao.len());

                    // 显示搜索结果
                    for (i, ditu) in jieguo.ditu_liebiao.iter().enumerate() {
                        println!("  {}. ID: {}, 名称: {}", i + 1, ditu.ditu_id, ditu.ditu_mingcheng);
                    }
                } else {
                    println!("✗ 地图搜索失败: {:?}", jieguo.cuowu_xinxi);
                }
            }
            Err(e) => {
                println!("✗ 地图搜索异常: {}", e);
            }
        }

        println!("=== 地图搜索功能测试完成 ===\n");
    }

    /// 测试缓存统计功能
    pub async fn ceshi_huancun_tongji() {
        println!("=== 开始测试缓存统计功能 ===");

        // 创建MySQL连接
        let mysql_lianjie = mysql_lianjie_guanli::new();

        // 创建Redis连接
        let redis_lianjie = redis_lianjie_guanli::new();
        let redis_kongzhi = ditu_redis_kongzhi::new(redis_lianjie);
        let liebiao_guanli = ditu_liebiao_guanli::new_with_redis(mysql_lianjie, redis_kongzhi);

        // 获取缓存统计
        match liebiao_guanli.huoqu_ditu_huancun_tongji().await {
            Ok(tongji) => {
                println!("✓ 缓存统计获取成功");
                println!("{}", tongji);
            }
            Err(e) => {
                println!("✗ 缓存统计获取失败: {}", e);
            }
        }

        println!("=== 缓存统计功能测试完成 ===\n");
    }

    /// 测试缓存清理功能
    pub async fn ceshi_qingchu_huancun() {
        println!("=== 开始测试缓存清理功能 ===");

        // 创建MySQL连接
        let mysql_lianjie = mysql_lianjie_guanli::new();

        // 创建Redis连接
        let redis_lianjie = redis_lianjie_guanli::new();
        let redis_kongzhi = ditu_redis_kongzhi::new(redis_lianjie);
        let liebiao_guanli = ditu_liebiao_guanli::new_with_redis(mysql_lianjie, redis_kongzhi);

        // 清理缓存
        match liebiao_guanli.qingchu_suoyou_ditu_huancun().await {
            Ok((quanbu_shanchu, liebiao_shanchu)) => {
                println!("✓ 缓存清理成功");
                println!("  - 清理地图全部信息缓存: {} 个", quanbu_shanchu);
                println!("  - 清理地图列表缓存: {} 个", liebiao_shanchu);
            }
            Err(e) => {
                println!("✗ 缓存清理失败: {}", e);
            }
        }

        println!("=== 缓存清理功能测试完成 ===\n");
    }

    /// 运行所有测试
    pub async fn yunxing_suoyou_ceshi() {
        println!("开始运行地图列表相关功能测试...\n");

        Self::ceshi_huoqu_ditu_liebiao().await;
        Self::ceshi_sousuo_ditu().await;
        Self::ceshi_huancun_tongji().await;
        Self::ceshi_qingchu_huancun().await;

        println!("所有地图列表测试完成！");
    }
}
