#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

/// 地图数据处理字符串常量管理类
pub struct ditu_zifuchuan_changliangguanli;

impl ditu_zifuchuan_changliangguanli {
    // ==================== 查询模式常量 ====================

    /// 全部信息查询模式
    pub const chaxun_moshi_quanbu_xinxi: &'static str = "quanbu_xinxi";

    // ==================== 数据库表名常量 ====================

    /// ditu_name表名
    pub const biao_ming_ditu_name: &'static str = "ditu_name";

    /// ditu_huizong表名
    pub const biao_ming_ditu_huizong: &'static str = "ditu_huizong";

    /// 指定字段支持表名（用于字段映射）
    pub const biao_ming_zhiding_ziduan_zhichi: &'static str = "zhiding_ziduan_zhichi";

    // ==================== 错误信息常量 ====================

    /// 地图不存在错误信息模板
    pub const cuowu_ditu_bucunzai: &'static str = "地图ID {} 不存在";

    /// 指定字段不在汇总表中错误信息
    pub const cuowu_ziduan_bu_zai_huizong_biao: &'static str = "指定的字段都不在汇总表中";

    /// 地图在汇总表中不存在错误信息模板
    pub const cuowu_ditu_zai_huizong_biao_bucunzai: &'static str = "地图ID {} 在汇总表中不存在";

    /// 地图基础信息不存在错误信息
    pub const cuowu_ditu_jiben_xinxi_bucunzai: &'static str = "地图基础信息不存在";

    /// 地图汇总信息不存在错误信息
    pub const cuowu_ditu_huizong_xinxi_bucunzai: &'static str = "地图汇总信息不存在";

    // ==================== Redis相关常量 ====================

    /// Redis键前缀 - 地图全部信息
    pub const redis_jian_qianzhui_ditu_quanbu: &'static str = "ditu_quanbu";

    /// Redis键模式 - 地图全部信息（用于批量删除）
    pub const redis_jian_moshi_ditu_quanbu: &'static str = "ditu_quanbu:*";

    /// 地图全部信息缓存时间：3天（259200秒）
    pub const ditu_quanbu_xinxi_huancun_shijian: u64 = 259200;

    /// Redis键前缀 - 地图列表
    pub const redis_jian_qianzhui_ditu_liebiao: &'static str = "ditu_liebiao";

    /// Redis键模式 - 地图列表（用于批量删除）
    pub const redis_jian_moshi_ditu_liebiao: &'static str = "ditu_liebiao:*";

    /// 地图列表缓存时间：5小时（18000秒）
    pub const ditu_liebiao_huancun_shijian: u64 = 18000;

    // ==================== 缓存统计信息常量 ====================

    /// 未启用Redis缓存提示信息
    pub const tongji_wei_qiyong_redis_huancun: &'static str = "未启用Redis缓存";

    // ==================== 地图列表相关错误信息常量 ====================

    /// 参数验证错误信息
    pub const cuowu_canshu_wuxiao: &'static str = "每页数量和当前页数必须大于0";

    /// 页数超出范围错误信息模板
    pub const cuowu_yeshu_chaochufanwei: &'static str = "当前页{}超出总页数{}";

    // ==================== Redis日志信息常量 ====================

    /// Redis获取成功日志模板 - 地图全部信息
    pub const rizhi_redis_huoqu_chenggong_quanbu: &'static str = "从Redis成功获取地图{}的全部信息";

    /// Redis缓存JSON解析失败日志模板 - 地图全部信息
    pub const rizhi_redis_json_jiexi_shibai_quanbu: &'static str = "地图{}的Redis缓存JSON解析失败: {}";

    /// Redis无缓存日志模板 - 地图全部信息
    pub const rizhi_redis_wu_huancun_quanbu: &'static str = "地图{}在Redis中无缓存";

    /// Redis存储成功日志模板 - 地图全部信息
    pub const rizhi_redis_cunchu_chenggong_quanbu: &'static str = "地图{}的全部信息已缓存到Redis，有效期3天";

    /// Redis存储失败日志模板 - 地图全部信息
    pub const rizhi_redis_cunchu_shibai_quanbu: &'static str = "地图{}的全部信息缓存到Redis失败: {}";

    /// Redis删除成功日志模板 - 地图全部信息
    pub const rizhi_redis_shanchu_chenggong_quanbu: &'static str = "成功删除地图{}的Redis缓存";

    /// Redis无需删除日志模板 - 地图全部信息
    pub const rizhi_redis_wuxu_shanchu_quanbu: &'static str = "地图{}在Redis中无缓存，无需删除";

    /// Redis删除失败日志模板 - 地图全部信息
    pub const rizhi_redis_shanchu_shibai_quanbu: &'static str = "删除地图{}的Redis缓存失败: {}";

    /// Redis清理成功日志模板 - 地图全部信息
    pub const rizhi_redis_qingchu_chenggong_quanbu: &'static str = "成功清理{}个地图全部信息的Redis缓存";

    /// Redis清理失败日志模板 - 地图全部信息
    pub const rizhi_redis_qingchu_shibai_quanbu: &'static str = "清理地图全部信息Redis缓存失败: {}";

    /// Redis获取统计失败日志模板
    pub const rizhi_redis_tongji_shibai: &'static str = "获取地图缓存统计信息失败: {}";

    /// Redis获取成功日志模板 - 地图列表
    pub const rizhi_redis_huoqu_chenggong_liebiao: &'static str = "从Redis成功获取地图列表，每页{}个，第{}页";

    /// Redis缓存JSON解析失败日志模板 - 地图列表
    pub const rizhi_redis_json_jiexi_shibai_liebiao: &'static str = "地图列表Redis缓存JSON解析失败: {}";

    /// Redis无缓存日志模板 - 地图列表
    pub const rizhi_redis_wu_huancun_liebiao: &'static str = "地图列表在Redis中无缓存，每页{}个，第{}页";

    /// Redis存储成功日志模板 - 地图列表
    pub const rizhi_redis_cunchu_chenggong_liebiao: &'static str = "地图列表已缓存到Redis，每页{}个，第{}页，有效期5小时";

    /// Redis存储失败日志模板 - 地图列表
    pub const rizhi_redis_cunchu_shibai_liebiao: &'static str = "地图列表缓存到Redis失败: {}";

    /// Redis清理成功日志模板 - 地图列表
    pub const rizhi_redis_qingchu_chenggong_liebiao: &'static str = "成功清理{}个地图列表的Redis缓存";

    /// Redis清理失败日志模板 - 地图列表
    pub const rizhi_redis_qingchu_shibai_liebiao: &'static str = "清理地图列表Redis缓存失败: {}";

    // ==================== 缓存统计信息常量 ====================

    /// 地图全部信息缓存统计模板
    pub const tongji_ditu_quanbu_huancun: &'static str = "地图缓存统计：\n- 地图全部信息缓存数量：{} 个\n- 缓存有效期：3天";

    /// 地图列表缓存统计模板
    pub const tongji_ditu_liebiao_huancun: &'static str = "地图列表缓存统计：\n- 地图列表缓存数量：{} 个\n- 缓存有效期：5小时";

    // ==================== 错误信息生成方法 ====================

    /// 生成地图不存在的错误信息
    pub fn shengcheng_cuowu_ditu_bucunzai(ditu_id: &str) -> String {
        Self::cuowu_ditu_bucunzai.replace("{}", ditu_id)
    }

    /// 生成地图在汇总表中不存在的错误信息
    pub fn shengcheng_cuowu_ditu_zai_huizong_biao_bucunzai(ditu_id: &str) -> String {
        Self::cuowu_ditu_zai_huizong_biao_bucunzai.replace("{}", ditu_id)
    }

    /// 生成页数超出范围的错误信息
    pub fn shengcheng_cuowu_yeshu_chaochufanwei(dangqian_ye: u32, zongyeshu: u32) -> String {
        format!("当前页{}超出总页数{}", dangqian_ye, zongyeshu)
    }

    // ==================== 缓存统计信息生成方法 ====================

    /// 生成地图全部信息缓存统计信息
    pub fn shengcheng_tongji_ditu_quanbu_huancun(shuliang: u64) -> String {
        Self::tongji_ditu_quanbu_huancun.replace("{}", &shuliang.to_string())
    }

    /// 生成地图列表缓存统计信息
    pub fn shengcheng_tongji_ditu_liebiao_huancun(shuliang: u64) -> String {
        Self::tongji_ditu_liebiao_huancun.replace("{}", &shuliang.to_string())
    }

    // ==================== Redis键名生成方法 ====================

    /// 生成地图全部信息的Redis键名
    pub fn shengcheng_redis_jian_ditu_quanbu(ditu_id: &str) -> String {
        format!("{}:{}", Self::redis_jian_qianzhui_ditu_quanbu, ditu_id)
    }

    /// 生成地图列表的Redis键名
    pub fn shengcheng_redis_jian_ditu_liebiao(meiye_shuliang: u32, dangqian_ye: u32) -> String {
        format!("{}:{}:{}", Self::redis_jian_qianzhui_ditu_liebiao, meiye_shuliang, dangqian_ye)
    }
}
